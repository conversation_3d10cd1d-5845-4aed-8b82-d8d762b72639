package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONUtil;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRecordDTO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import com.chis.zyjk.bigdata.alert.service.AlertRecordService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询预警记录组件
 * <p>
 * <b>getAlertRecord</b>
 */
@RequiredArgsConstructor
@LiteflowComponent(id = "getAlertRecord", name = "查询预警记录组件")
public class GetAlertRecordCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {

    private final AlertRecordService alertRecordService;

    @Override
    public void doProcess() {
        String deDupKeyValueListPath = config.getStr("deDupKeyValueListPath");
        // 获取预警记录去重键值列表
        List<String> deDupKeyValueList = Convert.toList(String.class, getInputDataByPath(deDupKeyValueListPath));
        if (StrUtil.isNotBlank(deDupKeyValueListPath) && ObjectUtil.isEmpty(deDupKeyValueList)) {
            outputResult(new ArrayList<>());
            return;
        }

        String queryWhereSql = getValueByExpression(config.getStr("queryWhereSql"));
        String queryOrderBy = getValueByExpression(config.getStr("queryOrderBy"));
        String queryGroupBy = getValueByExpression(config.getStr("queryGroupBy"));
        String queryTopN = getValueByExpression(config.getStr("queryTopN"));
        // 查询预警记录
        List<AlertRecordPO> alertRecords = queryAlertRecords(deDupKeyValueList, queryWhereSql,
                queryOrderBy, queryGroupBy, queryTopN);

        // 转换为DTO并输出结果
        List<AlertRecordDTO> result = convertToAlertRecordDTOs(alertRecords);
        outputResult(alertRecords);
    }

    /**
     * 查询预警记录
     */
    private List<AlertRecordPO> queryAlertRecords(List<String> deDupKeyValueList, String queryWhereSql,
                                                  String queryOrderBy, String queryGroupBy, String queryTopN) {
        // 根据是否有分组和排序条件选择查询方法
        if (StrUtil.isNotBlank(queryGroupBy) && StrUtil.isNotBlank(queryOrderBy) && StrUtil.isNotBlank(queryTopN)) {
            // 使用分组查询TopN
            int topN = Convert.toInt(queryTopN, 10);
            return alertRecordService.listTopNByCustomSql(queryWhereSql, deDupKeyValueList,
                    queryOrderBy, queryGroupBy, topN);
        } else {
            // 使用普通查询
            return alertRecordService.listByCustomSql(queryWhereSql, deDupKeyValueList, queryOrderBy);
        }
    }

    /**
     * 转换为AlertRecordDTO列表
     */
    private List<AlertRecordDTO> convertToAlertRecordDTOs(List<AlertRecordPO> alertRecords) {
        List<AlertRecordDTO> result = new ArrayList<>();
        for (AlertRecordPO alertRecord : alertRecords) {
            if (ObjectUtil.isEmpty(alertRecord)) {
                continue;
            }
            AlertRecordDTO dto = new AlertRecordDTO();
            // 基础字段映射
            dto.setAlertRecordId(alertRecord.getId());
            dto.setRuleCode(alertRecord.getRuleCode());
            dto.setDeDupValue(alertRecord.getDedupValue());
            dto.setChangeType(alertRecord.getChangeType());
            dto.setAlertLevel(alertRecord.getAlertLevel());
            dto.setAlertValue(alertRecord.getAlertValue());
            dto.setStatus(Convert.toStr(alertRecord.getStatus(), ""));
            dto.setAlertContent(alertRecord.getAlertContent());
            dto.setCreateTime(alertRecord.getCreateTime());
            dto.setUpdateTime(alertRecord.getUpdateTime());

            // JSON字段处理
            if (StrUtil.isNotBlank(alertRecord.getAlertJson())) {
                try {
                    dto.setAlertJson(JSONUtil.parseObj(alertRecord.getAlertJson()));
                } catch (Exception e) {
                    logger.warn("解析alertJson失败: {}", alertRecord.getAlertJson(), e);
                    throw new RuntimeException("解析alertJson失败");
                }
            }

            if (StrUtil.isNotBlank(alertRecord.getSourceData())) {
                try {
                    dto.setSourceData(JSONUtil.parseObj(alertRecord.getSourceData()));
                } catch (Exception e) {
                    logger.warn("解析sourceData失败: {}", alertRecord.getSourceData(), e);
                }
            }

            // 设置预警记录对象
            dto.setAlertRecord(alertRecord);

            result.add(dto);
        }
        return result;
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"queryWhereSql"};
    }

    /**
     * 输出查询结果
     *
     * @param result 查询结果
     */
    public void outputResult(List<AlertRecordPO> result) {
        ContextHelper.setContextData(this.getTag(), "result", result, cmp);
    }
}
