package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.project.frame.common.tools.json.JSONUtil;
import com.chis.zyjk.bigdata.alert.enums.DataProcessType;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertProcessResultDTO;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRecordDTO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import com.chis.zyjk.core.common.enums.base.StringCodeEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;

import java.util.ArrayList;
import java.util.List;

/**
 * 封装预警处理结果组件
 * <p>
 * <b>pakAlertProcess</b>
 */
@LiteflowComponent(id = "pakAlertProcess", name = "封装预警处理结果组件")
public class PakAlertProcessCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {

    @Override
    public void doProcess() {
        // 获取数据处理类型
        DataProcessType dataProcessType;
        try {
            dataProcessType = StringCodeEnum.fromCodeWithException(DataProcessType.class, data.getStr("type"));
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_PARAMETER_ERROR,
                    this
            );
        }

        // 获取预警处理结果
        String alertProcessDataPath = "tagData(" + config.getStr("genAlertProcessDataCmpTag") + ", result)";
        Object alertProcessDataObject = VariableReplacerUtils.resolveExpression(this.tag, alertProcessDataPath, contextBeanList);
        AlertProcessResultDTO alertProcessData = Convert.convert(AlertProcessResultDTO.class, alertProcessDataObject);
        if (ObjectUtil.isEmpty(alertProcessData) || ObjectUtil.isEmpty(alertProcessData.getAlertRecordMap())) {
            return;
        }

        // 获取输入结果
        List<Object> inputDataList = getInputDataByPath(config.getStr("inputDataPath"));
        if (ObjectUtil.isEmpty(inputDataList)) {
            return;
        }

        // 数据处理方式
        switch (dataProcessType) {
            case BUSINESS:
                List<JSONObject> businessDataList = Convert.toList(JSONObject.class, inputDataList);
                if (ObjectUtil.isEmpty(businessDataList)) {
                    throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_EXECUTION_FAILED, "业务数据转换失败", this);
                }
                pakAlertProcessByBusiness(alertProcessData, businessDataList);
                break;
            case ALERT:
                List<AlertRecordPO> alertRecordPOList = Convert.toList(AlertRecordPO.class, inputDataList);
                if (ObjectUtil.isEmpty(alertRecordPOList)) {
                    throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_EXECUTION_FAILED, "预警记录转换失败", this);
                }
                pakAlertProcessByAlert(alertProcessData, alertRecordPOList);
                break;
            default:
        }
    }

    /**
     * 封装预警实体
     *
     * @param result            结果集
     * @param alertRecordPOList 输入数据
     */
    private void pakAlertProcessByAlert(AlertProcessResultDTO result, List<AlertRecordPO> alertRecordPOList) {
        for (AlertRecordPO alertRecordPO : alertRecordPOList) {
            if (ObjectUtil.isEmpty(alertRecordPO)) {
                continue;
            }
            List<AlertRecordDTO> alertRecordDTOList = result.getAlertRecordMap().get(alertRecordPO.getDedupValue());
            if (ObjectUtil.isEmpty(alertRecordDTOList)) {
                continue;
            }

            for (AlertRecordDTO alertRecordDTO : alertRecordDTOList) {
                alertRecordDTO.setAlertRecord(alertRecordPO);
            }
        }
    }

    /**
     * 封装业务数据/预警值
     *
     * @param result           结果集
     * @param businessDataList 业务数据
     */
    private void pakAlertProcessByBusiness(AlertProcessResultDTO result, List<JSONObject> businessDataList) {
        // 获取去重键表达式
        String deDupKeyExpression = global.getDeDupKeyExpression();
        // 获取预警值表达式
        String alertValueExpression = global.getAlertValueExpression();
        // 封装去重键值和预警值
        for (JSONObject businessData : businessDataList) {
            if (ObjectUtil.isEmpty(businessData)) {
                continue;
            }
            ContextHelper.setContextData(this.getTag(), "optData", businessData, cmp);
            String deDupKey = "";
            // 封装去重键值
            try {
                deDupKey = getValueByExpression(deDupKeyExpression);
            } catch (Exception e) {
                throw LiteFlowExceptionHelper.createNodeException(
                        e,
                        LiteFlowErrorCode.EXPRESSION_ERROR,
                        LiteFlowErrorCode.EXPRESSION_ERROR + "，去重键表达式：[" + deDupKeyExpression + "]，：" + JSONUtil.toJsonStr(businessData),
                        this
                );
            }
            List<AlertRecordDTO> alertRecordDTOList = result.getAlertRecordMap().get(deDupKey);
            if (ObjectUtil.isEmpty(alertRecordDTOList)) {
                continue;
            }

            String alertValue = "";
            // 封装预警值
            if (StrUtil.isNotBlank(alertValueExpression)) {
                try {
                    alertValue = getValueByExpression(alertValueExpression);
                } catch (Exception e) {
                    throw LiteFlowExceptionHelper.createNodeException(
                            e,
                            LiteFlowErrorCode.EXPRESSION_ERROR,
                            LiteFlowErrorCode.EXPRESSION_ERROR + "，预警值表达式：[" + deDupKeyExpression + "]，：" + JSONUtil.toJsonStr(businessData),
                            this
                    );
                }
            }
            for (AlertRecordDTO alertRecordDTO : alertRecordDTOList) {
                alertRecordDTO.setSourceData(businessData);
                alertRecordDTO.setAlertValue(alertValue);
            }
        }
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"type", "genAlertProcessDataCmpTag", "inputDataPath"};
    }

}
