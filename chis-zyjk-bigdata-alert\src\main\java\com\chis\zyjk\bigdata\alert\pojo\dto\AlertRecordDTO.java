package com.chis.zyjk.bigdata.alert.pojo.dto;

import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 预警结果类
 */
@Data
public class AlertRecordDTO {
    /**
     * 预警记录ID
     */
    private String alertRecordId;

    /**
     * 预警规则编码
     */
    private String ruleCode;

    /**
     * 去重键值
     */
    private String deDupValue;

    /**
     * 变更类型
     */
    private String changeType;

    /**
     * 预警级别编码
     */
    private String alertLevel;

    /**
     * 预警值
     */
    private String alertValue;

    /**
     * 状态
     */
    private String status;

    /**
     * 预警内容
     */
    private String alertContent;

    /**
     * 自定义JSON
     */
     private JSONObject alertJson;

    /**
     * 预警源数据JSON
     */
    private JSONObject sourceData;

    /**
     * 通知内容
     */
    private String noticeContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最新预警记录主表对象
     */
    private AlertRecordPO alertRecord;
}
