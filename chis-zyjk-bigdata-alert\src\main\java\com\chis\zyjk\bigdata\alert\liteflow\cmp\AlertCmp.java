package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.json.JSONArray;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.enums.ProcessStrategy;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.utils.SpELExpressionUtils;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertProcessResultDTO;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRecordDTO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordLogPO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import com.yomahub.liteflow.annotation.LiteflowComponent;

import java.util.List;

/**
 * 预警组件
 * <p>
 * <b>alert</b>
 */
@LiteflowComponent(id = "alert", name = "预警组件")
public class AlertCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {


    @Override
    public void doProcess() {
        // 获取预警处理结果
        String alertProcessDataPath = "tagData(" + config.getStr("genAlertProcessDataCmpTag") + ", result)";
        Object alertProcessDataObject = VariableReplacerUtils.resolveExpression(this.tag, alertProcessDataPath, contextBeanList);
        AlertProcessResultDTO alertProcessData = Convert.convert(AlertProcessResultDTO.class, alertProcessDataObject);
        if (ObjectUtil.isEmpty(alertProcessData) || ObjectUtil.isEmpty(alertProcessData.getAlertRecordMap())) {
            return;
        }
        alertProcessData.getAlertRecordMap().forEach((deDupValue, alertRecord) -> {
            dealAlertRecord(alertProcessData, alertRecord);
        });

    }

    /**
     * 处理批量记录
     *
     * @param alertProcessData 预处理数据
     * @param alertRecord      预处理预警列表
     */
    private void dealAlertRecord(AlertProcessResultDTO alertProcessData, List<AlertRecordDTO> alertRecord) {
        alertRecord.forEach(alertRecordDTO -> processRecordWithExisting(alertProcessData, alertRecordDTO));
    }

    /**
     * 处理单条记录
     *
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     */
    private void processRecordWithExisting(AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        // 决定处理策略
        ProcessStrategy strategy = determineStrategy(alertRecordDTO);

        // 根据策略处理
        switch (strategy) {
            case CREATE:
                createNewAlert(alertProcessData, alertRecordDTO);
                break;
            case UPDATE:
                updateAlert(alertProcessData, alertRecordDTO);
                break;
            case DISPOSE:
                disposeAlert(alertProcessData, alertRecordDTO);
                break;
            default:
        }
    }

    /**
     * 决定处理策略
     *
     * @param alertRecordDTO 预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy determineStrategy(AlertRecordDTO alertRecordDTO) {
        JSONObject upgradeStrategy = config.getJSONObject("upgradeStrategy");
        // 无策略配置时的默认逻辑
        if (upgradeStrategy == null) {
            return ProcessStrategy.IGNORE;
        }

        // 直接执行策略链
        return executeStrategyChain(upgradeStrategy, alertRecordDTO);
    }

    /**
     * 执行策略链
     *
     * @param upgradeStrategy 升级策略配置
     * @param alertRecordDTO  预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy executeStrategyChain(JSONObject upgradeStrategy, AlertRecordDTO alertRecordDTO) {
        JSONArray strategies = upgradeStrategy.getJSONArray("strategies");
        if (ObjectUtil.isEmpty(strategies)) {
            return ProcessStrategy.IGNORE;
        }
        ContextHelper.setContextData(this.getTag(), "optData", alertRecordDTO, cmp);

        // 按顺序执行策略，直到返回非NO_ACTION
        for (int i = 0; i < strategies.size(); i++) {
            JSONObject strategy = strategies.getJSONObject(i);
            ProcessStrategy result = executeStrategy(strategy, alertRecordDTO);

            // 只有返回NO_ACTION时才继续下一个策略
            if (result != ProcessStrategy.NO_ACTION) {
                return result;
            }
        }

        // 所有策略都返回NO_ACTION
        return ProcessStrategy.NO_ACTION;
    }

    /**
     * 执行单个策略
     *
     * @param strategy       策略配置
     * @param alertRecordDTO 预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy executeStrategy(JSONObject strategy, AlertRecordDTO alertRecordDTO) {
        String strategyType = strategy.getStr("type");
        JSONObject strategyConfig = strategy.getJSONObject("config");

        switch (strategyType) {
            case "CONDITION_BASED":
                return executeConditionBasedStrategy(strategyConfig, alertRecordDTO);
            case "FIXED":
                return ProcessStrategy.valueOf(strategyConfig.getStr("action", "NO_ACTION"));
            default:
                return ProcessStrategy.NO_ACTION;
        }
    }

    /**
     * 执行条件表达式策略
     *
     * @param strategyConfig 策略配置
     * @param alertRecordDTO 预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy executeConditionBasedStrategy(JSONObject strategyConfig, AlertRecordDTO alertRecordDTO) {
        String condition = strategyConfig.getStr("condition");
        JSONObject actions = strategyConfig.getJSONObject("actions");

        condition = VariableReplacerUtils.replaceVariables(this.tag, condition, contextBeanList);
        // 使用SpEL 表达式工具类评估条件
        boolean matches = SpELExpressionUtils.evaluateCondition(condition, contextBeanList);
        String action = actions.getStr(matches ? "true" : "false", "NO_ACTION");
        return ProcessStrategy.valueOf(action);
    }

    /**
     * 创建新预警
     *
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     */
    private void createNewAlert(AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        // 构建预警记录
        AlertRecordPO alertRecord = buildAlertRecord(alertRecordDTO);
        AlertRecordLogPO logRecord = buildAlertLog(alertRecordDTO, "CREATE");

        // 存储在新增list中
        alertProcessData.getCreateList().add(alertRecord);
        alertProcessData.getLogList().add(logRecord);
    }

    /**
     * 更新预警
     *
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     */
    private void updateAlert(AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        if (ObjectUtil.isEmpty(alertRecordDTO.getAlertRecord())) {
            createNewAlert(alertProcessData, alertRecordDTO);
        } else {
            // 有主表记录：先更新主表记录，然后新增子表记录
            AlertRecordPO alertRecord = updateAlertRecord(alertRecordDTO);
            AlertRecordLogPO logRecord = buildAlertLog(alertRecordDTO, "UPDATE");

            alertProcessData.getUpdateList().add(alertRecord);
            alertProcessData.getLogList().add(logRecord);
        }
    }

    /**
     * 处置预警
     *
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     */
    private void disposeAlert(AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        if (ObjectUtil.isEmpty(alertRecordDTO.getAlertRecord())) {
            return;
        }
        AlertRecordPO alertRecord = alertRecordDTO.getAlertRecord();
        // 只更新主表记录的处置状态
        alertRecord.setStatus(1);
        AlertRecordLogPO logRecord = buildAlertLog(alertRecordDTO, "DISPOSE");

        alertProcessData.getDisposeList().add(alertRecord);
        alertProcessData.getLogList().add(logRecord);
    }

    /**
     * 构建预警记录
     *
     * @param alertRecordDTO 预处理预警数据
     * @return 预警记录
     */
    private AlertRecordPO buildAlertRecord(AlertRecordDTO alertRecordDTO) {
        AlertRecordPO alertRecord = new AlertRecordPO();
        alertRecordDTO.setAlertRecord(alertRecord);
        updateAlertRecord(alertRecordDTO);
        alertRecord.setRuleCode(alertRecordDTO.getRuleCode());
        alertRecord.setDeDupValue(alertRecordDTO.getDeDupValue());
        alertRecord.setChangeType(ProcessStrategy.CREATE.getCode());
        alertRecord.setStatus(0);
        return alertRecord;
    }

    /**
     * 更新预警记录
     *
     * @param alertRecordDTO 预处理预警数据
     */
    private AlertRecordPO updateAlertRecord(AlertRecordDTO alertRecordDTO) {
        AlertRecordPO alertRecord = alertRecordDTO.getAlertRecord();
        alertRecord.setChangeType(ProcessStrategy.UPDATE.getCode());
        alertRecord.setAlertLevel(alertRecordDTO.getAlertLevel());
        alertRecord.setAlertValue(alertRecordDTO.getAlertValue());
        alertRecord.setStatus(0);
        alertRecord.setAlertContent(generateAlertContent());
        alertRecord.setAlertJson(Convert.toStr(alertRecordDTO.getAlertJson(), ""));
        alertRecord.setSourceData(Convert.toStr(alertRecordDTO.getSourceData(), ""));
        return alertRecord;
    }

    /**
     * 构建预警日志记录
     *
     * @param alertRecordDTO 预处理预警数据
     * @param changeType     变更类型
     * @return 预警日志记录
     */
    private AlertRecordLogPO buildAlertLog(AlertRecordDTO alertRecordDTO, String changeType) {
        AlertRecordPO alertRecord = alertRecordDTO.getAlertRecord();
        AlertRecordLogPO logRecord = new AlertRecordLogPO();
        logRecord.setRuleCode(alertRecord.getRuleCode());
        logRecord.setDeDupValue(alertRecord.getDeDupValue());
        logRecord.setChangeType(changeType);
        logRecord.setAlertLevel(alertRecord.getAlertLevel());
        logRecord.setAlertValue(alertRecord.getAlertValue());
        logRecord.setStatus(alertRecord.getStatus());
        logRecord.setAlertContent(alertRecord.getAlertContent());
        logRecord.setAlertJson(alertRecord.getAlertJson());
        logRecord.setSourceData(alertRecord.getSourceData());
        return logRecord;
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"genAlertProcessDataCmpTag", "upgradeStrategy"};
    }
}
